# Chapter 05 - Doctor dan Staff Management

## Tujuan Chapter
Pada chapter ini, kita akan:
- Membuat Eloquent models untuk Doctor dan Staff
- Implementasi CRUD operations untuk doctor dan staff management
- Setup doctor profile dengan specialization dan schedule
- Membuat staff management dengan department assignment
- Implementasi doctor availability system

## Langkah 1: Create Doctor Model

### 1.1 Generate Doctor Model
```bash
php artisan make:model Hospital/Doctor
```

Edit `app/Models/Hospital/Doctor.php`:
```php
<?php

namespace App\Models\Hospital;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class Doctor extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'department_id',
        'doctor_id',
        'license_number',
        'specialization',
        'qualification',
        'experience_years',
        'phone',
        'bio',
        'consultation_fee',
        'available_days',
        'available_from',
        'available_to',
        'is_available',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'available_days' => 'array',
            'available_from' => 'datetime:H:i',
            'available_to' => 'datetime:H:i',
            'consultation_fee' => 'decimal:2',
            'is_available' => 'boolean',
            'is_active' => 'boolean',
        ];
    }

    // Generate doctor ID otomatis
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($doctor) {
            if (empty($doctor->doctor_id)) {
                $doctor->doctor_id = self::generateDoctorId();
            }
        });
    }

    private static function generateDoctorId(): string
    {
        $prefix = 'DR';
        $year = date('Y');
        
        $lastDoctor = self::where('doctor_id', 'like', $prefix . $year . '%')
                         ->orderBy('doctor_id', 'desc')
                         ->first();

        if ($lastDoctor) {
            $lastNumber = (int) substr($lastDoctor->doctor_id, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    public function medicalRecords()
    {
        return $this->hasMany(MedicalRecord::class);
    }

    // Accessors
    public function getFullNameAttribute(): string
    {
        return $this->user->full_name;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeAvailable($query)
    {
        return $query->where('is_available', true)->where('is_active', true);
    }

    public function scopeByDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    public function scopeBySpecialization($query, $specialization)
    {
        return $query->where('specialization', 'like', "%{$specialization}%");
    }

    // Helper methods
    public function isAvailableOnDay($day): bool
    {
        return in_array(strtolower($day), array_map('strtolower', $this->available_days ?? []));
    }

    public function getAvailableDaysText(): string
    {
        if (!$this->available_days) return 'Tidak ada jadwal';
        
        $days = [
            'monday' => 'Senin',
            'tuesday' => 'Selasa',
            'wednesday' => 'Rabu',
            'thursday' => 'Kamis',
            'friday' => 'Jumat',
            'saturday' => 'Sabtu',
            'sunday' => 'Minggu',
        ];

        $availableDays = array_map(function($day) use ($days) {
            return $days[strtolower($day)] ?? $day;
        }, $this->available_days);

        return implode(', ', $availableDays);
    }
}
```

## Langkah 2: Create Staff Model

### 2.1 Generate Staff Model
```bash
php artisan make:model Hospital/Staff
```

Edit `app/Models/Hospital/Staff.php`:
```php
<?php

namespace App\Models\Hospital;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class Staff extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'department_id',
        'staff_id',
        'position',
        'employee_type',
        'hire_date',
        'salary',
        'phone',
        'address',
        'emergency_contact_name',
        'emergency_contact_phone',
        'shift_schedule',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'hire_date' => 'date',
            'salary' => 'decimal:2',
            'shift_schedule' => 'array',
            'is_active' => 'boolean',
        ];
    }

    // Generate staff ID otomatis
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($staff) {
            if (empty($staff->staff_id)) {
                $staff->staff_id = self::generateStaffId();
            }
        });
    }

    private static function generateStaffId(): string
    {
        $prefix = 'ST';
        $year = date('Y');
        
        $lastStaff = self::where('staff_id', 'like', $prefix . $year . '%')
                        ->orderBy('staff_id', 'desc')
                        ->first();

        if ($lastStaff) {
            $lastNumber = (int) substr($lastStaff->staff_id, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    // Accessors
    public function getFullNameAttribute(): string
    {
        return $this->user->full_name;
    }

    public function getEmployeeTypeTextAttribute(): string
    {
        $types = [
            'full-time' => 'Penuh Waktu',
            'part-time' => 'Paruh Waktu',
            'contract' => 'Kontrak',
        ];

        return $types[$this->employee_type] ?? $this->employee_type;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    public function scopeByPosition($query, $position)
    {
        return $query->where('position', 'like', "%{$position}%");
    }

    public function scopeByEmployeeType($query, $type)
    {
        return $query->where('employee_type', $type);
    }
}
```

## Langkah 3: Create Department Model

### 3.1 Generate Department Model
```bash
php artisan make:model Hospital/Department
```

Edit `app/Models/Hospital/Department.php`:
```php
<?php

namespace App\Models\Hospital;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Department extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
        'location',
        'phone',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    // Relationships
    public function doctors()
    {
        return $this->hasMany(Doctor::class);
    }

    public function staff()
    {
        return $this->hasMany(Staff::class);
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Helper methods
    public function getTotalDoctorsAttribute(): int
    {
        return $this->doctors()->active()->count();
    }

    public function getTotalStaffAttribute(): int
    {
        return $this->staff()->active()->count();
    }

    public function getAvailableDoctorsAttribute()
    {
        return $this->doctors()->available()->get();
    }
}
```

## Langkah 4: Create Doctor Controller

### 4.1 Generate Controller
```bash
php artisan make:controller Hospital/DoctorController --resource
```

Edit `app/Http/Controllers/Hospital/DoctorController.php`:
```php
<?php

namespace App\Http\Controllers\Hospital;

use App\Http\Controllers\Controller;
use App\Models\Hospital\Doctor;
use App\Models\Hospital\Department;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DoctorController extends Controller
{
    public function index(Request $request)
    {
        $query = Doctor::with(['user', 'department']);

        // Search functionality
        if ($request->filled('search')) {
            $query->whereHas('user', function ($q) use ($request) {
                $q->where('first_name', 'like', "%{$request->search}%")
                  ->orWhere('last_name', 'like', "%{$request->search}%");
            })->orWhere('doctor_id', 'like', "%{$request->search}%")
              ->orWhere('specialization', 'like', "%{$request->search}%");
        }

        // Filter by department
        if ($request->filled('department')) {
            $query->byDepartment($request->department);
        }

        // Filter by availability
        if ($request->filled('availability')) {
            if ($request->availability === 'available') {
                $query->available();
            } elseif ($request->availability === 'unavailable') {
                $query->where('is_available', false);
            }
        }

        $doctors = $query->orderBy('created_at', 'desc')
                        ->paginate(15)
                        ->withQueryString();

        $departments = Department::active()->get();

        return Inertia::render('Hospital/Doctors/Index', [
            'doctors' => $doctors,
            'departments' => $departments,
            'filters' => $request->only(['search', 'department', 'availability']),
        ]);
    }

    public function create()
    {
        $departments = Department::active()->get();
        $users = User::where('role', 'doctor')
                    ->whereDoesntHave('doctor')
                    ->active()
                    ->get();

        return Inertia::render('Hospital/Doctors/Create', [
            'departments' => $departments,
            'users' => $users,
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'department_id' => 'required|exists:departments,id',
            'license_number' => 'required|string|max:50|unique:doctors,license_number',
            'specialization' => 'required|string|max:255',
            'qualification' => 'required|string|max:255',
            'experience_years' => 'required|integer|min:0',
            'phone' => 'required|string|max:15',
            'bio' => 'nullable|string',
            'consultation_fee' => 'required|numeric|min:0',
            'available_days' => 'required|array|min:1',
            'available_days.*' => 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'available_from' => 'required|date_format:H:i',
            'available_to' => 'required|date_format:H:i|after:available_from',
        ]);

        $doctor = Doctor::create($validated);

        return redirect()->route('doctors.show', $doctor)
                        ->with('success', 'Data dokter berhasil ditambahkan.');
    }

    public function show(Doctor $doctor)
    {
        $doctor->load(['user', 'department', 'appointments.patient']);

        return Inertia::render('Hospital/Doctors/Show', [
            'doctor' => $doctor,
        ]);
    }

    public function edit(Doctor $doctor)
    {
        $departments = Department::active()->get();
        $doctor->load(['user', 'department']);

        return Inertia::render('Hospital/Doctors/Edit', [
            'doctor' => $doctor,
            'departments' => $departments,
        ]);
    }

    public function update(Request $request, Doctor $doctor)
    {
        $validated = $request->validate([
            'department_id' => 'required|exists:departments,id',
            'license_number' => 'required|string|max:50|unique:doctors,license_number,' . $doctor->id,
            'specialization' => 'required|string|max:255',
            'qualification' => 'required|string|max:255',
            'experience_years' => 'required|integer|min:0',
            'phone' => 'required|string|max:15',
            'bio' => 'nullable|string',
            'consultation_fee' => 'required|numeric|min:0',
            'available_days' => 'required|array|min:1',
            'available_days.*' => 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'available_from' => 'required|date_format:H:i',
            'available_to' => 'required|date_format:H:i|after:available_from',
            'is_available' => 'boolean',
            'is_active' => 'boolean',
        ]);

        $doctor->update($validated);

        return redirect()->route('doctors.show', $doctor)
                        ->with('success', 'Data dokter berhasil diperbarui.');
    }

    public function destroy(Doctor $doctor)
    {
        $doctor->delete();

        return redirect()->route('doctors.index')
                        ->with('success', 'Data dokter berhasil dihapus.');
    }
}
```

## Langkah 5: Create Staff Controller

### 5.1 Generate Controller
```bash
php artisan make:controller Hospital/StaffController --resource
```

Edit `app/Http/Controllers/Hospital/StaffController.php`:
```php
<?php

namespace App\Http\Controllers\Hospital;

use App\Http\Controllers\Controller;
use App\Models\Hospital\Staff;
use App\Models\Hospital\Department;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class StaffController extends Controller
{
    public function index(Request $request)
    {
        $query = Staff::with(['user', 'department']);

        // Search functionality
        if ($request->filled('search')) {
            $query->whereHas('user', function ($q) use ($request) {
                $q->where('first_name', 'like', "%{$request->search}%")
                  ->orWhere('last_name', 'like', "%{$request->search}%");
            })->orWhere('staff_id', 'like', "%{$request->search}%")
              ->orWhere('position', 'like', "%{$request->search}%");
        }

        // Filter by department
        if ($request->filled('department')) {
            $query->byDepartment($request->department);
        }

        // Filter by employee type
        if ($request->filled('employee_type')) {
            $query->byEmployeeType($request->employee_type);
        }

        $staff = $query->orderBy('created_at', 'desc')
                      ->paginate(15)
                      ->withQueryString();

        $departments = Department::active()->get();

        return Inertia::render('Hospital/Staff/Index', [
            'staff' => $staff,
            'departments' => $departments,
            'filters' => $request->only(['search', 'department', 'employee_type']),
        ]);
    }

    public function create()
    {
        $departments = Department::active()->get();
        $users = User::whereIn('role', ['nurse', 'receptionist', 'pharmacist', 'lab_technician'])
                    ->whereDoesntHave('staff')
                    ->active()
                    ->get();

        return Inertia::render('Hospital/Staff/Create', [
            'departments' => $departments,
            'users' => $users,
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'department_id' => 'required|exists:departments,id',
            'position' => 'required|string|max:255',
            'employee_type' => 'required|in:full-time,part-time,contract',
            'hire_date' => 'required|date',
            'salary' => 'required|numeric|min:0',
            'phone' => 'required|string|max:15',
            'address' => 'required|string',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:15',
            'shift_schedule' => 'nullable|array',
        ]);

        $staff = Staff::create($validated);

        return redirect()->route('staff.show', $staff)
                        ->with('success', 'Data staff berhasil ditambahkan.');
    }

    public function show(Staff $staff)
    {
        $staff->load(['user', 'department']);

        return Inertia::render('Hospital/Staff/Show', [
            'staff' => $staff,
        ]);
    }

    public function edit(Staff $staff)
    {
        $departments = Department::active()->get();
        $staff->load(['user', 'department']);

        return Inertia::render('Hospital/Staff/Edit', [
            'staff' => $staff,
            'departments' => $departments,
        ]);
    }

    public function update(Request $request, Staff $staff)
    {
        $validated = $request->validate([
            'department_id' => 'required|exists:departments,id',
            'position' => 'required|string|max:255',
            'employee_type' => 'required|in:full-time,part-time,contract',
            'hire_date' => 'required|date',
            'salary' => 'required|numeric|min:0',
            'phone' => 'required|string|max:15',
            'address' => 'required|string',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:15',
            'shift_schedule' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        $staff->update($validated);

        return redirect()->route('staff.show', $staff)
                        ->with('success', 'Data staff berhasil diperbarui.');
    }

    public function destroy(Staff $staff)
    {
        $staff->delete();

        return redirect()->route('staff.index')
                        ->with('success', 'Data staff berhasil dihapus.');
    }
}
```

## Langkah 6: Add Routes

### 6.1 Update Routes
Edit `routes/web.php` dan tambahkan:
```php
// Doctor and Staff management routes
Route::middleware(['auth', 'role:admin'])->group(function () {
    Route::resource('doctors', DoctorController::class);
    Route::resource('staff', StaffController::class);
    Route::resource('departments', DepartmentController::class);
});

// Doctor can view their own profile
Route::middleware(['auth', 'role:doctor'])->group(function () {
    Route::get('/my-profile', [DoctorController::class, 'myProfile'])->name('doctors.my-profile');
    Route::put('/my-profile', [DoctorController::class, 'updateMyProfile'])->name('doctors.update-my-profile');
});
```

## Next Steps

Pada chapter selanjutnya, kita akan:
- Implementasi appointment scheduling system
- Membuat calendar view untuk appointments
- Setup appointment status management
- Implementasi notification system

## Checklist Completion

- [ ] Doctor model dengan relationships dan auto-ID
- [ ] Staff model dengan department assignment
- [ ] Department model dengan helper methods
- [ ] Doctor controller dengan CRUD operations
- [ ] Staff controller dengan filtering
- [ ] Doctor availability system
- [ ] Routes untuk doctor dan staff management

**Estimasi Waktu**: 90-120 menit

**Difficulty Level**: Intermediate-Advanced

**File yang Dibuat**:
- Doctor model
- Staff model  
- Department model
- DoctorController
- StaffController
- Routes untuk management
