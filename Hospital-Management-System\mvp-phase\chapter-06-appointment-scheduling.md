# Chapter 06 - Appointment Scheduling System

## Tujuan Chapter
Pada chapter ini, kita akan:
- Membuat Eloquent model untuk Appointment
- Implementasi appointment booking system
- Setup calendar view untuk appointments
- Membuat appointment status management
- Implementasi appointment validation dan conflict checking

## Langkah 1: Create Appointment Model

### 1.1 Generate Appointment Model
```bash
php artisan make:model Hospital/Appointment
```

Edit `app/Models/Hospital/Appointment.php`:
```php
<?php

namespace App\Models\Hospital;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Appointment extends Model
{
    use HasFactory;

    protected $fillable = [
        'appointment_number',
        'patient_id',
        'doctor_id',
        'department_id',
        'appointment_date',
        'appointment_type',
        'status',
        'reason_for_visit',
        'notes',
        'fee',
        'payment_status',
        'checked_in_at',
        'checked_out_at',
        'created_by',
    ];

    protected function casts(): array
    {
        return [
            'appointment_date' => 'datetime',
            'checked_in_at' => 'datetime',
            'checked_out_at' => 'datetime',
            'fee' => 'decimal:2',
        ];
    }

    // Generate appointment number otomatis
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($appointment) {
            if (empty($appointment->appointment_number)) {
                $appointment->appointment_number = self::generateAppointmentNumber();
            }
        });
    }

    private static function generateAppointmentNumber(): string
    {
        $prefix = 'APT';
        $date = date('Ymd');
        
        $lastAppointment = self::where('appointment_number', 'like', $prefix . $date . '%')
                              ->orderBy('appointment_number', 'desc')
                              ->first();

        if ($lastAppointment) {
            $lastNumber = (int) substr($lastAppointment->appointment_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $date . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function doctor()
    {
        return $this->belongsTo(Doctor::class);
    }

    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function medicalRecord()
    {
        return $this->hasOne(MedicalRecord::class);
    }

    // Accessors
    public function getAppointmentTimeAttribute(): string
    {
        return $this->appointment_date->format('H:i');
    }

    public function getAppointmentDateFormattedAttribute(): string
    {
        return $this->appointment_date->format('d/m/Y');
    }

    public function getStatusTextAttribute(): string
    {
        $statuses = [
            'scheduled' => 'Terjadwal',
            'confirmed' => 'Dikonfirmasi',
            'in_progress' => 'Sedang Berlangsung',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan',
            'no_show' => 'Tidak Hadir',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    public function getAppointmentTypeTextAttribute(): string
    {
        $types = [
            'consultation' => 'Konsultasi',
            'follow_up' => 'Kontrol',
            'emergency' => 'Darurat',
            'surgery' => 'Operasi',
        ];

        return $types[$this->appointment_type] ?? $this->appointment_type;
    }

    public function getPaymentStatusTextAttribute(): string
    {
        $statuses = [
            'pending' => 'Menunggu Pembayaran',
            'paid' => 'Sudah Dibayar',
            'cancelled' => 'Dibatalkan',
        ];

        return $statuses[$this->payment_status] ?? $this->payment_status;
    }

    // Scopes
    public function scopeToday($query)
    {
        return $query->whereDate('appointment_date', today());
    }

    public function scopeUpcoming($query)
    {
        return $query->where('appointment_date', '>=', now());
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByDoctor($query, $doctorId)
    {
        return $query->where('doctor_id', $doctorId);
    }

    public function scopeByPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('appointment_date', [$startDate, $endDate]);
    }

    // Helper methods
    public function canBeCheckedIn(): bool
    {
        return in_array($this->status, ['scheduled', 'confirmed']) && 
               $this->appointment_date->isToday() &&
               !$this->checked_in_at;
    }

    public function canBeCheckedOut(): bool
    {
        return $this->status === 'in_progress' && 
               $this->checked_in_at && 
               !$this->checked_out_at;
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['scheduled', 'confirmed']) && 
               $this->appointment_date->isFuture();
    }

    public function getDurationAttribute(): ?int
    {
        if ($this->checked_in_at && $this->checked_out_at) {
            return $this->checked_in_at->diffInMinutes($this->checked_out_at);
        }
        return null;
    }

    // Static methods untuk conflict checking
    public static function hasConflict($doctorId, $appointmentDate, $excludeId = null): bool
    {
        $query = self::where('doctor_id', $doctorId)
                    ->where('appointment_date', $appointmentDate)
                    ->whereNotIn('status', ['cancelled', 'no_show']);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    public static function getAvailableSlots($doctorId, $date): array
    {
        $doctor = Doctor::find($doctorId);
        if (!$doctor) return [];

        $dayName = Carbon::parse($date)->format('l');
        if (!$doctor->isAvailableOnDay($dayName)) {
            return [];
        }

        $startTime = Carbon::parse($date . ' ' . $doctor->available_from->format('H:i'));
        $endTime = Carbon::parse($date . ' ' . $doctor->available_to->format('H:i'));
        
        $slots = [];
        $slotDuration = 30; // 30 minutes per slot

        while ($startTime->lt($endTime)) {
            $slotTime = $startTime->copy();
            
            // Check if slot is available
            if (!self::hasConflict($doctorId, $slotTime)) {
                $slots[] = [
                    'time' => $slotTime->format('H:i'),
                    'datetime' => $slotTime->toDateTimeString(),
                    'available' => true,
                ];
            }

            $startTime->addMinutes($slotDuration);
        }

        return $slots;
    }
}
```

## Langkah 2: Create Appointment Controller

### 2.1 Generate Controller
```bash
php artisan make:controller Hospital/AppointmentController --resource
```

Edit `app/Http/Controllers/Hospital/AppointmentController.php`:
```php
<?php

namespace App\Http\Controllers\Hospital;

use App\Http\Controllers\Controller;
use App\Models\Hospital\Appointment;
use App\Models\Hospital\Patient;
use App\Models\Hospital\Doctor;
use App\Models\Hospital\Department;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class AppointmentController extends Controller
{
    public function index(Request $request)
    {
        $query = Appointment::with(['patient', 'doctor.user', 'department']);

        // Filter by date range
        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->byDateRange($request->date_from, $request->date_to);
        } elseif ($request->filled('date')) {
            $query->whereDate('appointment_date', $request->date);
        } else {
            // Default to today's appointments
            $query->today();
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->byStatus($request->status);
        }

        // Filter by doctor
        if ($request->filled('doctor')) {
            $query->byDoctor($request->doctor);
        }

        // Filter by department
        if ($request->filled('department')) {
            $query->where('department_id', $request->department);
        }

        $appointments = $query->orderBy('appointment_date', 'asc')
                             ->paginate(20)
                             ->withQueryString();

        $doctors = Doctor::with('user')->active()->get();
        $departments = Department::active()->get();

        return Inertia::render('Hospital/Appointments/Index', [
            'appointments' => $appointments,
            'doctors' => $doctors,
            'departments' => $departments,
            'filters' => $request->only(['date', 'date_from', 'date_to', 'status', 'doctor', 'department']),
        ]);
    }

    public function create(Request $request)
    {
        $patients = Patient::active()->get();
        $doctors = Doctor::with(['user', 'department'])->available()->get();
        $departments = Department::active()->get();

        // If patient_id is provided, pre-select the patient
        $selectedPatient = null;
        if ($request->filled('patient_id')) {
            $selectedPatient = Patient::find($request->patient_id);
        }

        return Inertia::render('Hospital/Appointments/Create', [
            'patients' => $patients,
            'doctors' => $doctors,
            'departments' => $departments,
            'selectedPatient' => $selectedPatient,
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'patient_id' => 'required|exists:patients,id',
            'doctor_id' => 'required|exists:doctors,id',
            'department_id' => 'required|exists:departments,id',
            'appointment_date' => 'required|date|after:now',
            'appointment_type' => 'required|in:consultation,follow_up,emergency,surgery',
            'reason_for_visit' => 'required|string',
            'notes' => 'nullable|string',
        ]);

        // Check for conflicts
        if (Appointment::hasConflict($validated['doctor_id'], $validated['appointment_date'])) {
            return back()->withErrors([
                'appointment_date' => 'Waktu appointment sudah terboking. Silakan pilih waktu lain.'
            ]);
        }

        // Get doctor's consultation fee
        $doctor = Doctor::find($validated['doctor_id']);
        $validated['fee'] = $doctor->consultation_fee;
        $validated['status'] = 'scheduled';
        $validated['payment_status'] = 'pending';
        $validated['created_by'] = auth()->id();

        $appointment = Appointment::create($validated);

        return redirect()->route('appointments.show', $appointment)
                        ->with('success', 'Appointment berhasil dibuat.');
    }

    public function show(Appointment $appointment)
    {
        $appointment->load(['patient', 'doctor.user', 'department', 'createdBy', 'medicalRecord']);

        return Inertia::render('Hospital/Appointments/Show', [
            'appointment' => $appointment,
        ]);
    }

    public function edit(Appointment $appointment)
    {
        if (!in_array($appointment->status, ['scheduled', 'confirmed'])) {
            return back()->with('error', 'Appointment ini tidak dapat diedit.');
        }

        $patients = Patient::active()->get();
        $doctors = Doctor::with(['user', 'department'])->available()->get();
        $departments = Department::active()->get();

        $appointment->load(['patient', 'doctor', 'department']);

        return Inertia::render('Hospital/Appointments/Edit', [
            'appointment' => $appointment,
            'patients' => $patients,
            'doctors' => $doctors,
            'departments' => $departments,
        ]);
    }

    public function update(Request $request, Appointment $appointment)
    {
        if (!in_array($appointment->status, ['scheduled', 'confirmed'])) {
            return back()->with('error', 'Appointment ini tidak dapat diedit.');
        }

        $validated = $request->validate([
            'patient_id' => 'required|exists:patients,id',
            'doctor_id' => 'required|exists:doctors,id',
            'department_id' => 'required|exists:departments,id',
            'appointment_date' => 'required|date|after:now',
            'appointment_type' => 'required|in:consultation,follow_up,emergency,surgery',
            'reason_for_visit' => 'required|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:scheduled,confirmed,cancelled',
        ]);

        // Check for conflicts (excluding current appointment)
        if (Appointment::hasConflict($validated['doctor_id'], $validated['appointment_date'], $appointment->id)) {
            return back()->withErrors([
                'appointment_date' => 'Waktu appointment sudah terboking. Silakan pilih waktu lain.'
            ]);
        }

        $appointment->update($validated);

        return redirect()->route('appointments.show', $appointment)
                        ->with('success', 'Appointment berhasil diperbarui.');
    }

    public function destroy(Appointment $appointment)
    {
        if (!$appointment->canBeCancelled()) {
            return back()->with('error', 'Appointment ini tidak dapat dibatalkan.');
        }

        $appointment->update(['status' => 'cancelled']);

        return redirect()->route('appointments.index')
                        ->with('success', 'Appointment berhasil dibatalkan.');
    }

    // Additional methods for appointment management
    public function checkIn(Appointment $appointment)
    {
        if (!$appointment->canBeCheckedIn()) {
            return back()->with('error', 'Appointment tidak dapat di-check in.');
        }

        $appointment->update([
            'status' => 'in_progress',
            'checked_in_at' => now(),
        ]);

        return back()->with('success', 'Pasien berhasil di-check in.');
    }

    public function checkOut(Appointment $appointment)
    {
        if (!$appointment->canBeCheckedOut()) {
            return back()->with('error', 'Appointment tidak dapat di-check out.');
        }

        $appointment->update([
            'status' => 'completed',
            'checked_out_at' => now(),
        ]);

        return back()->with('success', 'Pasien berhasil di-check out.');
    }

    public function getAvailableSlots(Request $request)
    {
        $request->validate([
            'doctor_id' => 'required|exists:doctors,id',
            'date' => 'required|date|after_or_equal:today',
        ]);

        $slots = Appointment::getAvailableSlots($request->doctor_id, $request->date);

        return response()->json(['slots' => $slots]);
    }

    public function calendar(Request $request)
    {
        $startDate = $request->get('start', now()->startOfMonth());
        $endDate = $request->get('end', now()->endOfMonth());

        $appointments = Appointment::with(['patient', 'doctor.user'])
                                  ->byDateRange($startDate, $endDate)
                                  ->get()
                                  ->map(function ($appointment) {
                                      return [
                                          'id' => $appointment->id,
                                          'title' => $appointment->patient->full_name . ' - ' . $appointment->doctor->user->full_name,
                                          'start' => $appointment->appointment_date->toISOString(),
                                          'backgroundColor' => $this->getStatusColor($appointment->status),
                                          'borderColor' => $this->getStatusColor($appointment->status),
                                          'extendedProps' => [
                                              'appointment_number' => $appointment->appointment_number,
                                              'patient_name' => $appointment->patient->full_name,
                                              'doctor_name' => $appointment->doctor->user->full_name,
                                              'status' => $appointment->status_text,
                                              'type' => $appointment->appointment_type_text,
                                          ],
                                      ];
                                  });

        return Inertia::render('Hospital/Appointments/Calendar', [
            'appointments' => $appointments,
        ]);
    }

    private function getStatusColor($status): string
    {
        $colors = [
            'scheduled' => '#3B82F6',
            'confirmed' => '#10B981',
            'in_progress' => '#F59E0B',
            'completed' => '#6B7280',
            'cancelled' => '#EF4444',
            'no_show' => '#8B5CF6',
        ];

        return $colors[$status] ?? '#6B7280';
    }
}
```

## Langkah 3: Add Appointment Routes

### 3.1 Update Routes
Edit `routes/web.php` dan tambahkan:
```php
// Appointment management routes
Route::middleware(['auth', 'role:admin,receptionist,nurse,doctor'])->group(function () {
    Route::resource('appointments', AppointmentController::class);
    Route::get('/appointments-calendar', [AppointmentController::class, 'calendar'])->name('appointments.calendar');
    Route::get('/available-slots', [AppointmentController::class, 'getAvailableSlots'])->name('appointments.available-slots');
    
    // Appointment actions
    Route::post('/appointments/{appointment}/check-in', [AppointmentController::class, 'checkIn'])->name('appointments.check-in');
    Route::post('/appointments/{appointment}/check-out', [AppointmentController::class, 'checkOut'])->name('appointments.check-out');
});
```

## Langkah 4: Create Appointment Seeder

### 4.1 Create Seeder
```bash
php artisan make:seeder AppointmentSeeder
```

Edit `database/seeders/AppointmentSeeder.php`:
```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Hospital\Appointment;
use App\Models\Hospital\Patient;
use App\Models\Hospital\Doctor;
use App\Models\Hospital\Department;
use App\Models\User;
use Carbon\Carbon;

class AppointmentSeeder extends Seeder
{
    public function run(): void
    {
        // Create sample patients first
        $patients = Patient::factory(10)->create();
        
        // Create sample doctors
        $doctorUsers = User::factory(3)->create(['role' => 'doctor']);
        $department = Department::first();
        
        $doctors = [];
        foreach ($doctorUsers as $user) {
            $doctors[] = Doctor::create([
                'user_id' => $user->id,
                'department_id' => $department->id,
                'license_number' => 'LIC' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
                'specialization' => 'Dokter Umum',
                'qualification' => 'S1 Kedokteran',
                'experience_years' => rand(1, 20),
                'phone' => '081234567' . rand(100, 999),
                'consultation_fee' => rand(100000, 500000),
                'available_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
                'available_from' => '08:00',
                'available_to' => '16:00',
            ]);
        }

        // Create sample appointments
        $admin = User::where('role', 'admin')->first();
        
        foreach (range(1, 20) as $i) {
            $appointmentDate = Carbon::today()->addDays(rand(-7, 14))->setHour(rand(8, 15))->setMinute(rand(0, 1) * 30);
            
            Appointment::create([
                'patient_id' => $patients->random()->id,
                'doctor_id' => collect($doctors)->random()->id,
                'department_id' => $department->id,
                'appointment_date' => $appointmentDate,
                'appointment_type' => collect(['consultation', 'follow_up', 'emergency'])->random(),
                'status' => collect(['scheduled', 'confirmed', 'completed', 'cancelled'])->random(),
                'reason_for_visit' => 'Konsultasi kesehatan rutin',
                'fee' => rand(100000, 300000),
                'payment_status' => collect(['pending', 'paid'])->random(),
                'created_by' => $admin->id,
            ]);
        }
    }
}
```

## Next Steps

Pada chapter selanjutnya, kita akan:
- Implementasi medical records system
- Membuat patient medical history tracking
- Setup prescription management
- Implementasi vital signs recording

## Checklist Completion

- [ ] Appointment model dengan auto-numbering
- [ ] Appointment controller dengan CRUD operations
- [ ] Conflict checking untuk appointment scheduling
- [ ] Available slots API endpoint
- [ ] Check-in/check-out functionality
- [ ] Calendar view untuk appointments
- [ ] Appointment status management
- [ ] Sample data seeder

**Estimasi Waktu**: 120-150 menit

**Difficulty Level**: Advanced

**File yang Dibuat**:
- Appointment model
- AppointmentController
- Appointment routes
- AppointmentSeeder
