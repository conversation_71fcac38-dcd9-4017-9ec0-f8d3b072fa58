# Chapter 07 - Medical Records Management

## Tujuan Chapter
Pada chapter ini, kita akan:
- Membuat Eloquent model untuk Medical Records
- Implementasi medical records CRUD operations
- Setup vital signs recording system
- Membuat prescription management
- Implementasi medical history tracking

## Langkah 1: Create Medical Record Model

### 1.1 Generate Medical Record Model
```bash
php artisan make:model Hospital/MedicalRecord
```

Edit `app/Models/Hospital/MedicalRecord.php`:
```php
<?php

namespace App\Models\Hospital;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MedicalRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'record_number',
        'patient_id',
        'doctor_id',
        'appointment_id',
        'visit_date',
        'chief_complaint',
        'history_of_present_illness',
        'physical_examination',
        'diagnosis',
        'treatment_plan',
        'medications',
        'lab_results',
        'notes',
        'next_visit_date',
        'vital_signs_temperature',
        'vital_signs_blood_pressure_systolic',
        'vital_signs_blood_pressure_diastolic',
        'vital_signs_heart_rate',
        'vital_signs_respiratory_rate',
        'vital_signs_weight',
        'vital_signs_height',
    ];

    protected function casts(): array
    {
        return [
            'visit_date' => 'datetime',
            'next_visit_date' => 'datetime',
            'vital_signs_temperature' => 'decimal:1',
            'vital_signs_weight' => 'decimal:2',
            'vital_signs_height' => 'decimal:2',
        ];
    }

    // Generate record number otomatis
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($record) {
            if (empty($record->record_number)) {
                $record->record_number = self::generateRecordNumber();
            }
        });
    }

    private static function generateRecordNumber(): string
    {
        $prefix = 'MR';
        $date = date('Ymd');
        
        $lastRecord = self::where('record_number', 'like', $prefix . $date . '%')
                         ->orderBy('record_number', 'desc')
                         ->first();

        if ($lastRecord) {
            $lastNumber = (int) substr($lastRecord->record_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $date . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function doctor()
    {
        return $this->belongsTo(Doctor::class);
    }

    public function appointment()
    {
        return $this->belongsTo(Appointment::class);
    }

    // Accessors
    public function getBloodPressureAttribute(): ?string
    {
        if ($this->vital_signs_blood_pressure_systolic && $this->vital_signs_blood_pressure_diastolic) {
            return $this->vital_signs_blood_pressure_systolic . '/' . $this->vital_signs_blood_pressure_diastolic;
        }
        return null;
    }

    public function getBmiAttribute(): ?float
    {
        if ($this->vital_signs_weight && $this->vital_signs_height) {
            $heightInMeters = $this->vital_signs_height / 100;
            return round($this->vital_signs_weight / ($heightInMeters * $heightInMeters), 2);
        }
        return null;
    }

    public function getBmiCategoryAttribute(): ?string
    {
        $bmi = $this->bmi;
        if (!$bmi) return null;

        if ($bmi < 18.5) return 'Underweight';
        if ($bmi < 25) return 'Normal';
        if ($bmi < 30) return 'Overweight';
        return 'Obese';
    }

    public function getVitalSignsSummaryAttribute(): array
    {
        return [
            'temperature' => $this->vital_signs_temperature ? $this->vital_signs_temperature . '°C' : null,
            'blood_pressure' => $this->blood_pressure ? $this->blood_pressure . ' mmHg' : null,
            'heart_rate' => $this->vital_signs_heart_rate ? $this->vital_signs_heart_rate . ' bpm' : null,
            'respiratory_rate' => $this->vital_signs_respiratory_rate ? $this->vital_signs_respiratory_rate . ' /min' : null,
            'weight' => $this->vital_signs_weight ? $this->vital_signs_weight . ' kg' : null,
            'height' => $this->vital_signs_height ? $this->vital_signs_height . ' cm' : null,
            'bmi' => $this->bmi ? $this->bmi . ' (' . $this->bmi_category . ')' : null,
        ];
    }

    // Scopes
    public function scopeByPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    public function scopeByDoctor($query, $doctorId)
    {
        return $query->where('doctor_id', $doctorId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('visit_date', [$startDate, $endDate]);
    }

    public function scopeRecent($query, $limit = 10)
    {
        return $query->orderBy('visit_date', 'desc')->limit($limit);
    }
}
```

## Langkah 2: Create Medical Record Controller

### 2.1 Generate Controller
```bash
php artisan make:controller Hospital/MedicalRecordController --resource
```

Edit `app/Http/Controllers/Hospital/MedicalRecordController.php`:
```php
<?php

namespace App\Http\Controllers\Hospital;

use App\Http\Controllers\Controller;
use App\Models\Hospital\MedicalRecord;
use App\Models\Hospital\Patient;
use App\Models\Hospital\Doctor;
use App\Models\Hospital\Appointment;
use Illuminate\Http\Request;
use Inertia\Inertia;

class MedicalRecordController extends Controller
{
    public function index(Request $request)
    {
        $query = MedicalRecord::with(['patient', 'doctor.user', 'appointment']);

        // Filter by patient
        if ($request->filled('patient_id')) {
            $query->byPatient($request->patient_id);
        }

        // Filter by doctor
        if ($request->filled('doctor_id')) {
            $query->byDoctor($request->doctor_id);
        }

        // Filter by date range
        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->byDateRange($request->date_from, $request->date_to);
        }

        // Search functionality
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('record_number', 'like', "%{$request->search}%")
                  ->orWhere('diagnosis', 'like', "%{$request->search}%")
                  ->orWhere('chief_complaint', 'like', "%{$request->search}%")
                  ->orWhereHas('patient', function ($patientQuery) use ($request) {
                      $patientQuery->where('first_name', 'like', "%{$request->search}%")
                                  ->orWhere('last_name', 'like', "%{$request->search}%")
                                  ->orWhere('patient_id', 'like', "%{$request->search}%");
                  });
            });
        }

        $medicalRecords = $query->orderBy('visit_date', 'desc')
                               ->paginate(15)
                               ->withQueryString();

        $patients = Patient::active()->get();
        $doctors = Doctor::with('user')->active()->get();

        return Inertia::render('Hospital/MedicalRecords/Index', [
            'medicalRecords' => $medicalRecords,
            'patients' => $patients,
            'doctors' => $doctors,
            'filters' => $request->only(['patient_id', 'doctor_id', 'date_from', 'date_to', 'search']),
        ]);
    }

    public function create(Request $request)
    {
        $patients = Patient::active()->get();
        $doctors = Doctor::with('user')->active()->get();
        
        // Pre-select data if coming from appointment
        $selectedAppointment = null;
        $selectedPatient = null;
        $selectedDoctor = null;

        if ($request->filled('appointment_id')) {
            $selectedAppointment = Appointment::with(['patient', 'doctor.user'])
                                             ->find($request->appointment_id);
            if ($selectedAppointment) {
                $selectedPatient = $selectedAppointment->patient;
                $selectedDoctor = $selectedAppointment->doctor;
            }
        } elseif ($request->filled('patient_id')) {
            $selectedPatient = Patient::find($request->patient_id);
        }

        return Inertia::render('Hospital/MedicalRecords/Create', [
            'patients' => $patients,
            'doctors' => $doctors,
            'selectedAppointment' => $selectedAppointment,
            'selectedPatient' => $selectedPatient,
            'selectedDoctor' => $selectedDoctor,
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'patient_id' => 'required|exists:patients,id',
            'doctor_id' => 'required|exists:doctors,id',
            'appointment_id' => 'nullable|exists:appointments,id',
            'visit_date' => 'required|date',
            'chief_complaint' => 'required|string',
            'history_of_present_illness' => 'required|string',
            'physical_examination' => 'required|string',
            'diagnosis' => 'required|string',
            'treatment_plan' => 'required|string',
            'medications' => 'nullable|string',
            'lab_results' => 'nullable|string',
            'notes' => 'nullable|string',
            'next_visit_date' => 'nullable|date|after:visit_date',
            
            // Vital signs
            'vital_signs_temperature' => 'nullable|numeric|between:30,45',
            'vital_signs_blood_pressure_systolic' => 'nullable|integer|between:70,250',
            'vital_signs_blood_pressure_diastolic' => 'nullable|integer|between:40,150',
            'vital_signs_heart_rate' => 'nullable|integer|between:40,200',
            'vital_signs_respiratory_rate' => 'nullable|integer|between:10,60',
            'vital_signs_weight' => 'nullable|numeric|between:1,300',
            'vital_signs_height' => 'nullable|numeric|between:30,250',
        ]);

        $medicalRecord = MedicalRecord::create($validated);

        // Update appointment status if linked
        if ($medicalRecord->appointment_id) {
            $medicalRecord->appointment->update(['status' => 'completed']);
        }

        return redirect()->route('medical-records.show', $medicalRecord)
                        ->with('success', 'Rekam medis berhasil dibuat.');
    }

    public function show(MedicalRecord $medicalRecord)
    {
        $medicalRecord->load(['patient', 'doctor.user', 'appointment']);

        // Get patient's medical history
        $patientHistory = MedicalRecord::byPatient($medicalRecord->patient_id)
                                      ->with(['doctor.user'])
                                      ->where('id', '!=', $medicalRecord->id)
                                      ->orderBy('visit_date', 'desc')
                                      ->limit(5)
                                      ->get();

        return Inertia::render('Hospital/MedicalRecords/Show', [
            'medicalRecord' => $medicalRecord,
            'patientHistory' => $patientHistory,
        ]);
    }

    public function edit(MedicalRecord $medicalRecord)
    {
        $patients = Patient::active()->get();
        $doctors = Doctor::with('user')->active()->get();
        
        $medicalRecord->load(['patient', 'doctor', 'appointment']);

        return Inertia::render('Hospital/MedicalRecords/Edit', [
            'medicalRecord' => $medicalRecord,
            'patients' => $patients,
            'doctors' => $doctors,
        ]);
    }

    public function update(Request $request, MedicalRecord $medicalRecord)
    {
        $validated = $request->validate([
            'patient_id' => 'required|exists:patients,id',
            'doctor_id' => 'required|exists:doctors,id',
            'appointment_id' => 'nullable|exists:appointments,id',
            'visit_date' => 'required|date',
            'chief_complaint' => 'required|string',
            'history_of_present_illness' => 'required|string',
            'physical_examination' => 'required|string',
            'diagnosis' => 'required|string',
            'treatment_plan' => 'required|string',
            'medications' => 'nullable|string',
            'lab_results' => 'nullable|string',
            'notes' => 'nullable|string',
            'next_visit_date' => 'nullable|date|after:visit_date',
            
            // Vital signs
            'vital_signs_temperature' => 'nullable|numeric|between:30,45',
            'vital_signs_blood_pressure_systolic' => 'nullable|integer|between:70,250',
            'vital_signs_blood_pressure_diastolic' => 'nullable|integer|between:40,150',
            'vital_signs_heart_rate' => 'nullable|integer|between:40,200',
            'vital_signs_respiratory_rate' => 'nullable|integer|between:10,60',
            'vital_signs_weight' => 'nullable|numeric|between:1,300',
            'vital_signs_height' => 'nullable|numeric|between:30,250',
        ]);

        $medicalRecord->update($validated);

        return redirect()->route('medical-records.show', $medicalRecord)
                        ->with('success', 'Rekam medis berhasil diperbarui.');
    }

    public function destroy(MedicalRecord $medicalRecord)
    {
        $medicalRecord->delete();

        return redirect()->route('medical-records.index')
                        ->with('success', 'Rekam medis berhasil dihapus.');
    }

    // Additional methods
    public function patientHistory($patientId)
    {
        $patient = Patient::findOrFail($patientId);
        
        $medicalRecords = MedicalRecord::byPatient($patientId)
                                      ->with(['doctor.user', 'appointment'])
                                      ->orderBy('visit_date', 'desc')
                                      ->paginate(10);

        return Inertia::render('Hospital/MedicalRecords/PatientHistory', [
            'patient' => $patient,
            'medicalRecords' => $medicalRecords,
        ]);
    }

    public function printRecord(MedicalRecord $medicalRecord)
    {
        $medicalRecord->load(['patient', 'doctor.user', 'appointment']);

        return Inertia::render('Hospital/MedicalRecords/Print', [
            'medicalRecord' => $medicalRecord,
        ]);
    }
}
```

## Langkah 3: Create Prescription Model

### 3.1 Generate Prescription Model
```bash
php artisan make:model Hospital/Prescription -m
```

Edit migration `database/migrations/xxxx_create_prescriptions_table.php`:
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('prescriptions', function (Blueprint $table) {
            $table->id();
            $table->string('prescription_number', 20)->unique();
            $table->foreignId('medical_record_id')->constrained()->onDelete('cascade');
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained()->onDelete('restrict');
            $table->json('medications'); // Array of medications with dosage, frequency, duration
            $table->text('instructions')->nullable();
            $table->enum('status', ['pending', 'dispensed', 'completed', 'cancelled'])->default('pending');
            $table->datetime('dispensed_at')->nullable();
            $table->foreignId('dispensed_by')->nullable()->constrained('users');
            $table->text('pharmacist_notes')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('prescriptions');
    }
};
```

Edit `app/Models/Hospital/Prescription.php`:
```php
<?php

namespace App\Models\Hospital;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Prescription extends Model
{
    use HasFactory;

    protected $fillable = [
        'prescription_number',
        'medical_record_id',
        'patient_id',
        'doctor_id',
        'medications',
        'instructions',
        'status',
        'dispensed_at',
        'dispensed_by',
        'pharmacist_notes',
    ];

    protected function casts(): array
    {
        return [
            'medications' => 'array',
            'dispensed_at' => 'datetime',
        ];
    }

    // Generate prescription number otomatis
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($prescription) {
            if (empty($prescription->prescription_number)) {
                $prescription->prescription_number = self::generatePrescriptionNumber();
            }
        });
    }

    private static function generatePrescriptionNumber(): string
    {
        $prefix = 'RX';
        $date = date('Ymd');
        
        $lastPrescription = self::where('prescription_number', 'like', $prefix . $date . '%')
                               ->orderBy('prescription_number', 'desc')
                               ->first();

        if ($lastPrescription) {
            $lastNumber = (int) substr($lastPrescription->prescription_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $date . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function medicalRecord()
    {
        return $this->belongsTo(MedicalRecord::class);
    }

    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function doctor()
    {
        return $this->belongsTo(Doctor::class);
    }

    public function dispensedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'dispensed_by');
    }

    // Accessors
    public function getStatusTextAttribute(): string
    {
        $statuses = [
            'pending' => 'Menunggu',
            'dispensed' => 'Sudah Diserahkan',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeByPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    public function scopeByDoctor($query, $doctorId)
    {
        return $query->where('doctor_id', $doctorId);
    }
}
```

## Langkah 4: Add Medical Records Routes

### 4.1 Update Routes
Edit `routes/web.php` dan tambahkan:
```php
// Medical Records routes
Route::middleware(['auth', 'role:admin,doctor,nurse'])->group(function () {
    Route::resource('medical-records', MedicalRecordController::class);
    Route::get('/patients/{patient}/medical-history', [MedicalRecordController::class, 'patientHistory'])
         ->name('patients.medical-history');
    Route::get('/medical-records/{medicalRecord}/print', [MedicalRecordController::class, 'printRecord'])
         ->name('medical-records.print');
});

// Prescription routes
Route::middleware(['auth', 'role:admin,doctor,pharmacist'])->group(function () {
    Route::resource('prescriptions', PrescriptionController::class);
});
```

## Next Steps

Pada chapter selanjutnya, kita akan:
- Implementasi dashboard dengan metrics dan statistics
- Membuat reporting system
- Setup notification system
- Finalisasi MVP testing

## Checklist Completion

- [ ] Medical Record model dengan auto-numbering
- [ ] Medical Record controller dengan CRUD operations
- [ ] Vital signs recording system
- [ ] BMI calculation dan categorization
- [ ] Prescription model dan system
- [ ] Patient medical history tracking
- [ ] Print functionality untuk medical records
- [ ] Comprehensive validation untuk medical data

**Estimasi Waktu**: 120-150 menit

**Difficulty Level**: Advanced

**File yang Dibuat**:
- MedicalRecord model
- Prescription model
- MedicalRecordController
- Medical records routes
- Prescription migration
